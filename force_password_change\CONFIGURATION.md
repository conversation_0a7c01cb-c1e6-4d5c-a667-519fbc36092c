# دليل التكوين المتقدم - Force Password Change Module

## إعدادات النظام

يمكن تخصيص سلوك الموديول من خلال معاملات النظام التالية:

### 1. الحد الأدنى لطول كلمة المرور

```python
# في Python shell أو من خلال Settings > Technical > Parameters > System Parameters
env['ir.config_parameter'].set_param('force_password_change.min_password_length', '10')
```

**القيم المقبولة:** أي رقم صحيح (افتراضي: 8)

### 2. تفعيل تلقائي للمستخدمين الجدد

```python
# لتعطيل التفعيل التلقائي
env['ir.config_parameter'].set_param('force_password_change.auto_enable_for_new_users', 'False')

# لتفعيل التفعيل التلقائي (افتراضي)
env['ir.config_parameter'].set_param('force_password_change.auto_enable_for_new_users', 'True')
```

### 3. عنوان صفحة تغيير كلمة المرور

```python
# تخصيص العنوان
env['ir.config_parameter'].set_param('force_password_change.page_title', 'تغيير كلمة المرور مطلوب')
```

### 4. رسالة مخصصة

```python
# تخصيص الرسالة
env['ir.config_parameter'].set_param(
    'force_password_change.custom_message', 
    'لأسباب أمنية، يجب عليك تغيير كلمة المرور قبل الوصول للنظام.'
)
```

## التكوين المتقدم

### 1. تخصيص قواعد كلمة المرور

يمكنك إضافة قواعد إضافية لكلمة المرور عبر تعديل نموذج المستخدم:

```python
# في ملف Python مخصص
from odoo import models, api, _
from odoo.exceptions import ValidationError
import re

class ResUsersCustom(models.Model):
    _inherit = 'res.users'
    
    @api.constrains('password')
    def _check_password_strength(self):
        for user in self:
            if user.password:
                password = user.password
                
                # التحقق من وجود أرقام
                if not re.search(r'\d', password):
                    raise ValidationError(_('Password must contain at least one number'))
                
                # التحقق من وجود أحرف كبيرة
                if not re.search(r'[A-Z]', password):
                    raise ValidationError(_('Password must contain at least one uppercase letter'))
                
                # التحقق من وجود رموز خاصة
                if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                    raise ValidationError(_('Password must contain at least one special character'))
```

### 2. تخصيص فترة انتهاء كلمة المرور

```python
# إضافة حقل انتهاء كلمة المرور
class ResUsersPasswordExpiry(models.Model):
    _inherit = 'res.users'
    
    password_expiry_date = fields.Datetime(
        string='Password Expiry Date',
        help='Date when password expires'
    )
    
    @api.model
    def create(self, vals):
        user = super().create(vals)
        # تعيين تاريخ انتهاء كلمة المرور (90 يوم من الآن)
        expiry_date = fields.Datetime.now() + timedelta(days=90)
        user.password_expiry_date = expiry_date
        return user
```

### 3. تخصيص رسائل البريد الإلكتروني

```xml
<!-- في ملف data.xml -->
<record id="email_template_password_change_required" model="mail.template">
    <field name="name">Password Change Required</field>
    <field name="model_id" ref="base.model_res_users"/>
    <field name="subject">تغيير كلمة المرور مطلوب - ${object.name}</field>
    <field name="body_html"><![CDATA[
        <p>مرحباً ${object.name},</p>
        <p>يجب عليك تغيير كلمة المرور الخاصة بك عند تسجيل الدخول التالي.</p>
        <p>رابط تسجيل الدخول: <a href="${ctx.get('base_url', '')}/web/login">تسجيل الدخول</a></p>
    ]]></field>
</record>
```

### 4. تكامل مع أنظمة المصادقة الخارجية

```python
# للتكامل مع LDAP أو OAuth
class ResUsersAuth(models.Model):
    _inherit = 'res.users'
    
    @api.model
    def _auth_oauth_signin(self, provider, validation, params):
        # تخطي متطلب تغيير كلمة المرور للمستخدمين من OAuth
        user = super()._auth_oauth_signin(provider, validation, params)
        if user and user.must_change_password:
            user.must_change_password = False
        return user
```

## إعدادات الأمان المتقدمة

### 1. تسجيل محاولات تغيير كلمة المرور

```python
# إضافة تسجيل مفصل
import logging
_logger = logging.getLogger(__name__)

class ResUsersLogging(models.Model):
    _inherit = 'res.users'
    
    def change_password(self, old_passwd, new_passwd):
        _logger.info(f'Password change attempt for user {self.login} from IP {request.httprequest.environ.get("REMOTE_ADDR")}')
        try:
            result = super().change_password(old_passwd, new_passwd)
            _logger.info(f'Password change successful for user {self.login}')
            return result
        except Exception as e:
            _logger.warning(f'Password change failed for user {self.login}: {str(e)}')
            raise
```

### 2. حد أقصى لمحاولات تغيير كلمة المرور

```python
class ResUsersAttempts(models.Model):
    _inherit = 'res.users'
    
    password_change_attempts = fields.Integer(default=0)
    password_change_locked_until = fields.Datetime()
    
    def change_password(self, old_passwd, new_passwd):
        # التحقق من القفل
        if self.password_change_locked_until and self.password_change_locked_until > fields.Datetime.now():
            raise ValidationError(_('Account temporarily locked. Try again later.'))
        
        try:
            result = super().change_password(old_passwd, new_passwd)
            # إعادة تعيين المحاولات عند النجاح
            self.password_change_attempts = 0
            self.password_change_locked_until = False
            return result
        except AccessDenied:
            # زيادة عدد المحاولات
            self.password_change_attempts += 1
            if self.password_change_attempts >= 5:
                # قفل الحساب لمدة 30 دقيقة
                self.password_change_locked_until = fields.Datetime.now() + timedelta(minutes=30)
            raise
```

## تخصيص الواجهة

### 1. تخصيص CSS

```css
/* في ملف static/src/css/force_password_change.css */
.force-password-change-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.force-password-change-card {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

.force-password-change-header {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
}
```

### 2. تخصيص JavaScript

```javascript
// في ملف static/src/js/force_password_change.js
odoo.define('force_password_change.form', function (require) {
    'use strict';
    
    var core = require('web.core');
    var _t = core._t;
    
    $(document).ready(function() {
        // إضافة مؤشر قوة كلمة المرور
        $('#new_password').on('input', function() {
            var password = $(this).val();
            var strength = calculatePasswordStrength(password);
            updatePasswordStrengthIndicator(strength);
        });
    });
    
    function calculatePasswordStrength(password) {
        var score = 0;
        if (password.length >= 8) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;
        return score;
    }
});
```

## إعدادات قاعدة البيانات

### 1. فهرسة للأداء

```sql
-- إضافة فهرس لتحسين الأداء
CREATE INDEX idx_res_users_must_change_password ON res_users(must_change_password) WHERE must_change_password = true;
```

### 2. تنظيف دوري

```python
# مهمة مجدولة لتنظيف البيانات القديمة
@api.model
def _cleanup_old_password_requirements(self):
    # إزالة متطلب تغيير كلمة المرور للمستخدمين غير النشطين
    inactive_users = self.search([
        ('must_change_password', '=', True),
        ('active', '=', False)
    ])
    inactive_users.write({'must_change_password': False})
```

## مراقبة ومتابعة

### 1. تقارير الاستخدام

```python
# تقرير المستخدمين الذين يحتاجون لتغيير كلمة المرور
@api.model
def get_password_change_report(self):
    users = self.search([('must_change_password', '=', True)])
    return {
        'total_users': len(users),
        'users_by_department': users.read_group(
            [('must_change_password', '=', True)],
            ['department_id'],
            ['department_id']
        )
    }
```

### 2. تنبيهات تلقائية

```python
# إرسال تنبيهات للمديرين
@api.model
def _send_admin_alerts(self):
    users_count = self.search_count([('must_change_password', '=', True)])
    if users_count > 10:  # إذا كان هناك أكثر من 10 مستخدمين
        # إرسال تنبيه للمديرين
        admin_users = self.search([('groups_id', 'in', [self.env.ref('base.group_system').id])])
        # منطق إرسال التنبيه
```

## النسخ الاحتياطي والاستعادة

### 1. نسخ احتياطي للإعدادات

```bash
# نسخ احتياطي لمعاملات النظام
pg_dump -h localhost -U odoo -t ir_config_parameter odoo_db > force_password_change_config_backup.sql
```

### 2. استعادة الإعدادات

```bash
# استعادة معاملات النظام
psql -h localhost -U odoo -d odoo_db < force_password_change_config_backup.sql
```

هذا الدليل يوفر إعدادات متقدمة لتخصيص الموديول حسب احتياجاتك المحددة.
