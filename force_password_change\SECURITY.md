# دليل الأمان وأفضل الممارسات - Force Password Change Module

## نظرة عامة على الأمان

هذا الموديول مصمم مع التركيز على الأمان ويتبع أفضل الممارسات في أمان المعلومات.

## الميزات الأمنية المدمجة

### 1. حماية CSRF
- جميع النماذج محمية ضد هجمات Cross-Site Request Forgery
- استخدام tokens آمنة للتحقق من صحة الطلبات

### 2. التحقق من كلمة المرور
- التحقق من كلمة المرور الحالية قبل السماح بالتغيير
- منع استخدام كلمة المرور القديمة نفسها

### 3. تسجيل شامل للأحداث
- تسجيل جميع محاولات تغيير كلمة المرور
- تسجيل عناوين IP للمراجعة الأمنية
- تسجيل المحاولات الناجحة والفاشلة

### 4. إدارة الجلسات
- إدارة آمنة لجلسات المستخدمين
- تنظيف الجلسات عند تغيير كلمة المرور

## أفضل الممارسات للنشر

### 1. إعدادات كلمة المرور

```python
# تعيين حد أدنى قوي لطول كلمة المرور
env['ir.config_parameter'].set_param('force_password_change.min_password_length', '12')

# تفعيل قواعد إضافية لكلمة المرور
class ResUsersSecure(models.Model):
    _inherit = 'res.users'
    
    @api.constrains('password')
    def _check_password_complexity(self):
        for user in self:
            if user.password:
                password = user.password
                
                # التحقق من التعقيد
                if len(password) < 12:
                    raise ValidationError(_('Password must be at least 12 characters'))
                
                if not re.search(r'[A-Z]', password):
                    raise ValidationError(_('Password must contain uppercase letters'))
                
                if not re.search(r'[a-z]', password):
                    raise ValidationError(_('Password must contain lowercase letters'))
                
                if not re.search(r'\d', password):
                    raise ValidationError(_('Password must contain numbers'))
                
                if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                    raise ValidationError(_('Password must contain special characters'))
```

### 2. مراقبة الأمان

```python
# إضافة مراقبة للمحاولات المشبوهة
class SecurityMonitoring(models.Model):
    _inherit = 'res.users'
    
    failed_password_attempts = fields.Integer(default=0)
    last_failed_attempt = fields.Datetime()
    account_locked_until = fields.Datetime()
    
    def change_password(self, old_passwd, new_passwd):
        # التحقق من قفل الحساب
        if self.account_locked_until and self.account_locked_until > fields.Datetime.now():
            raise ValidationError(_('Account is temporarily locked'))
        
        try:
            result = super().change_password(old_passwd, new_passwd)
            # إعادة تعيين المحاولات الفاشلة عند النجاح
            self.write({
                'failed_password_attempts': 0,
                'last_failed_attempt': False,
                'account_locked_until': False
            })
            return result
        except AccessDenied:
            # تسجيل المحاولة الفاشلة
            self._record_failed_attempt()
            raise
    
    def _record_failed_attempt(self):
        attempts = self.failed_password_attempts + 1
        values = {
            'failed_password_attempts': attempts,
            'last_failed_attempt': fields.Datetime.now()
        }
        
        # قفل الحساب بعد 5 محاولات فاشلة
        if attempts >= 5:
            values['account_locked_until'] = fields.Datetime.now() + timedelta(hours=1)
        
        self.sudo().write(values)
        
        # تسجيل في السجل
        _logger.warning(f'Failed password attempt #{attempts} for user {self.login} from IP {request.httprequest.environ.get("REMOTE_ADDR")}')
```

### 3. تشفير البيانات الحساسة

```python
# تشفير البيانات الحساسة في قاعدة البيانات
from cryptography.fernet import Fernet
import base64

class SecureDataStorage(models.Model):
    _inherit = 'res.users'
    
    @api.model
    def _get_encryption_key(self):
        """الحصول على مفتاح التشفير من متغيرات البيئة"""
        import os
        key = os.environ.get('ODOO_ENCRYPTION_KEY')
        if not key:
            raise ValueError('ODOO_ENCRYPTION_KEY environment variable not set')
        return key.encode()
    
    def _encrypt_sensitive_data(self, data):
        """تشفير البيانات الحساسة"""
        if not data:
            return data
        
        key = self._get_encryption_key()
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()
    
    def _decrypt_sensitive_data(self, encrypted_data):
        """فك تشفير البيانات"""
        if not encrypted_data:
            return encrypted_data
        
        key = self._get_encryption_key()
        f = Fernet(key)
        decoded_data = base64.b64decode(encrypted_data.encode())
        return f.decrypt(decoded_data).decode()
```

## إعدادات الخادم الآمنة

### 1. إعدادات Nginx

```nginx
# إعدادات أمان إضافية لـ Nginx
server {
    listen 443 ssl http2;
    server_name your-odoo-domain.com;
    
    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;
    
    # Rate Limiting for password change endpoint
    location /web/force_password_change {
        limit_req zone=password_change burst=5 nodelay;
        proxy_pass http://odoo;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

# Rate limiting zone
http {
    limit_req_zone $binary_remote_addr zone=password_change:10m rate=10r/m;
}
```

### 2. إعدادات قاعدة البيانات

```sql
-- إنشاء مستخدم قاعدة بيانات مخصص للموديول
CREATE USER force_password_user WITH PASSWORD 'secure_random_password';

-- منح صلاحيات محدودة فقط
GRANT SELECT, INSERT, UPDATE ON res_users TO force_password_user;
GRANT SELECT, INSERT, UPDATE ON ir_config_parameter TO force_password_user;

-- تفعيل تسجيل الاستعلامات الحساسة
ALTER SYSTEM SET log_statement = 'mod';
ALTER SYSTEM SET log_min_duration_statement = 1000;
```

## مراجعة الأمان الدورية

### 1. فحص السجلات

```bash
# فحص محاولات تغيير كلمة المرور المشبوهة
grep -i "password.*change" /var/log/odoo/odoo.log | grep -E "(failed|error|exception)"

# فحص محاولات الوصول من عناوين IP مشبوهة
grep -i "force_password_change" /var/log/nginx/access.log | awk '{print $1}' | sort | uniq -c | sort -nr

# فحص الأخطاء الأمنية
grep -i "security\|csrf\|xss\|injection" /var/log/odoo/odoo.log
```

### 2. اختبار الاختراق

```python
# اختبار مقاومة هجمات القوة الغاشمة
def test_brute_force_protection():
    user = env['res.users'].search([('login', '=', '<EMAIL>')])
    
    # محاولة 10 مرات بكلمة مرور خاطئة
    for i in range(10):
        try:
            user.change_password('wrong_password', 'new_password')
        except AccessDenied:
            pass
    
    # يجب أن يكون الحساب مقفل الآن
    assert user.account_locked_until > fields.Datetime.now()

# اختبار حماية CSRF
def test_csrf_protection():
    # محاولة إرسال طلب بدون CSRF token
    # يجب أن يفشل الطلب
    pass
```

## التوافق مع معايير الأمان

### 1. GDPR (اللائحة العامة لحماية البيانات)

```python
# إضافة إمكانية حذف البيانات الشخصية
class GDPRCompliance(models.Model):
    _inherit = 'res.users'
    
    def anonymize_user_data(self):
        """إخفاء هوية بيانات المستخدم للامتثال لـ GDPR"""
        self.ensure_one()
        
        # إخفاء البيانات الشخصية
        self.write({
            'name': f'Anonymized User {self.id}',
            'email': f'anonymized{self.id}@deleted.local',
            'phone': False,
            'mobile': False,
            'must_change_password': False,
        })
        
        # تسجيل العملية
        _logger.info(f'User data anonymized for GDPR compliance: {self.id}')
```

### 2. ISO 27001

```python
# تطبيق ضوابط ISO 27001
class ISO27001Controls(models.Model):
    _inherit = 'res.users'
    
    password_history = fields.Text(help='Encrypted password history for compliance')
    last_password_change = fields.Datetime()
    password_expiry_notified = fields.Boolean(default=False)
    
    def _check_password_history(self, new_password):
        """التحقق من عدم استخدام كلمة مرور سابقة"""
        if self.password_history:
            # فك تشفير تاريخ كلمات المرور والتحقق
            # منطق التحقق من التاريخ
            pass
    
    def _update_password_history(self, old_password):
        """تحديث تاريخ كلمات المرور"""
        # تشفير وحفظ كلمة المرور القديمة
        # الاحتفاظ بآخر 12 كلمة مرور
        pass
```

## إجراءات الطوارئ

### 1. إعادة تعيين شاملة لكلمات المرور

```python
@api.model
def emergency_password_reset(self):
    """إعادة تعيين طارئة لجميع كلمات المرور"""
    # للاستخدام في حالات الطوارئ الأمنية فقط
    
    # تسجيل العملية
    _logger.critical('EMERGENCY: Mass password reset initiated')
    
    # فرض تغيير كلمة المرور لجميع المستخدمين النشطين
    active_users = self.search([('active', '=', True)])
    active_users.write({'must_change_password': True})
    
    # إرسال تنبيهات للمديرين
    admin_users = self.search([('groups_id', 'in', [self.env.ref('base.group_system').id])])
    for admin in admin_users:
        # إرسال تنبيه طارئ
        pass
    
    return len(active_users)
```

### 2. تعطيل الموديول في حالة الطوارئ

```python
@api.model
def emergency_disable_module(self):
    """تعطيل الموديول في حالة الطوارئ"""
    # إزالة متطلب تغيير كلمة المرور من جميع المستخدمين
    all_users = self.search([('must_change_password', '=', True)])
    all_users.write({'must_change_password': False})
    
    # تسجيل العملية
    _logger.critical('EMERGENCY: Force password change module disabled')
    
    return True
```

## التحديثات الأمنية

### 1. مراقبة الثغرات الأمنية

```bash
# فحص دوري للثغرات الأمنية
pip install safety
safety check

# فحص تبعيات Odoo
pip-audit
```

### 2. تحديث الموديول

```python
# آلية التحديث الآمن
@api.model
def secure_module_update(self):
    """تحديث آمن للموديول"""
    
    # نسخ احتياطي للبيانات قبل التحديث
    self._backup_module_data()
    
    # تطبيق التحديثات الأمنية
    self._apply_security_patches()
    
    # التحقق من سلامة البيانات بعد التحديث
    self._verify_data_integrity()
```

## خلاصة الأمان

هذا الموديول يوفر مستوى عالي من الأمان عند اتباع هذه الإرشادات:

1. **استخدم HTTPS دائماً** في بيئة الإنتاج
2. **فعل جميع ميزات الأمان** المذكورة أعلاه
3. **راقب السجلات بانتظام** للكشف عن الأنشطة المشبوهة
4. **حدث الموديول بانتظام** للحصول على آخر التحديثات الأمنية
5. **اختبر النسخ الاحتياطية** بانتظام
6. **درب المستخدمين** على أفضل ممارسات الأمان

تذكر: الأمان عملية مستمرة وليس حدث لمرة واحدة.
