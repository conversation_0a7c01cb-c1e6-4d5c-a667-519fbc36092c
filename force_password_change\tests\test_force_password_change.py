# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import AccessDenied
import logging

_logger = logging.getLogger(__name__)


class TestForcePasswordChange(TransactionCase):
    """Test cases for Force Password Change module"""

    def setUp(self):
        super(TestForcePasswordChange, self).setUp()
        self.Users = self.env['res.users']
        
        # Create a test user
        self.test_user_vals = {
            'name': 'Test User',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
            'password': 'testpassword123',
        }

    def test_new_user_must_change_password_flag(self):
        """Test that new users have must_change_password set to True"""
        user = self.Users.create(self.test_user_vals)
        self.assertTrue(user.must_change_password, 
                       "New user should have must_change_password set to True")

    def test_explicit_must_change_password_false(self):
        """Test that explicitly setting must_change_password to False works"""
        vals = self.test_user_vals.copy()
        vals['must_change_password'] = False
        user = self.Users.create(vals)
        self.assertFalse(user.must_change_password, 
                        "User with explicit must_change_password=False should not be forced")

    def test_password_change_clears_flag(self):
        """Test that changing password clears the must_change_password flag"""
        user = self.Users.create(self.test_user_vals)
        self.assertTrue(user.must_change_password)
        
        # Change password using change_password method
        user.change_password('testpassword123', 'newpassword123')
        self.assertFalse(user.must_change_password, 
                        "must_change_password should be False after password change")

    def test_password_write_clears_flag(self):
        """Test that writing password field clears the must_change_password flag"""
        user = self.Users.create(self.test_user_vals)
        self.assertTrue(user.must_change_password)
        
        # Change password using write method
        user.write({'password': 'newpassword123'})
        self.assertFalse(user.must_change_password, 
                        "must_change_password should be False after password write")

    def test_force_password_change_method(self):
        """Test the force_password_change method"""
        vals = self.test_user_vals.copy()
        vals['must_change_password'] = False
        user = self.Users.create(vals)
        
        result = user.force_password_change()
        self.assertTrue(user.must_change_password, 
                       "force_password_change should set flag to True")
        self.assertEqual(result['type'], 'ir.actions.client', 
                        "Should return notification action")

    def test_clear_password_change_requirement_method(self):
        """Test the clear_password_change_requirement method"""
        user = self.Users.create(self.test_user_vals)
        self.assertTrue(user.must_change_password)
        
        result = user.clear_password_change_requirement()
        self.assertFalse(user.must_change_password, 
                        "clear_password_change_requirement should set flag to False")
        self.assertEqual(result['type'], 'ir.actions.client', 
                        "Should return notification action")

    def test_user_field_exists(self):
        """Test that the must_change_password field exists and is accessible"""
        user = self.Users.create(self.test_user_vals)
        
        # Test field exists
        self.assertTrue(hasattr(user, 'must_change_password'), 
                       "must_change_password field should exist")
        
        # Test field is boolean
        self.assertIsInstance(user.must_change_password, bool, 
                             "must_change_password should be boolean")

    def test_admin_user_not_affected(self):
        """Test that admin user is not affected by default behavior"""
        admin_user = self.env.ref('base.user_admin')
        original_flag = admin_user.must_change_password
        
        # Update admin user (should not change the flag automatically)
        admin_user.write({'name': 'Updated Admin'})
        self.assertEqual(admin_user.must_change_password, original_flag, 
                        "Admin user flag should not change on update")

    def test_multiple_users_creation(self):
        """Test creating multiple users"""
        users_data = [
            {'name': 'User 1', 'login': '<EMAIL>', 'password': 'pass123'},
            {'name': 'User 2', 'login': '<EMAIL>', 'password': 'pass123'},
            {'name': 'User 3', 'login': '<EMAIL>', 'password': 'pass123'},
        ]
        
        for user_data in users_data:
            user = self.Users.create(user_data)
            self.assertTrue(user.must_change_password, 
                           f"User {user.name} should have must_change_password=True")

    def test_logging_functionality(self):
        """Test that logging works correctly"""
        with self.assertLogs('odoo.addons.force_password_change.models.res_users', level='INFO') as log:
            user = self.Users.create(self.test_user_vals)
            self.assertIn('New user created', log.output[0])
            self.assertIn(user.login, log.output[0])
            
        with self.assertLogs('odoo.addons.force_password_change.models.res_users', level='INFO') as log:
            user.change_password('testpassword123', 'newpassword123')
            self.assertIn('Password changed', log.output[0])
            self.assertIn(user.login, log.output[0])

    def test_integration_with_web_controller(self):
        """Test integration with web controller"""
        # This would require more complex setup with HTTP requests
        # For now, we test the basic controller logic
        user = self.Users.create(self.test_user_vals)
        self.assertTrue(user.must_change_password)

        # Simulate password change through controller logic
        user.change_password('testpassword123', 'newpassword456')
        self.assertFalse(user.must_change_password)

    def test_bulk_operations(self):
        """Test bulk operations on multiple users"""
        # Create multiple users
        users_data = []
        for i in range(5):
            users_data.append({
                'name': f'Test User {i}',
                'login': f'testuser{i}@example.com',
                'password': 'testpass123',
            })

        users = self.Users.create(users_data)

        # All should have must_change_password = True
        for user in users:
            self.assertTrue(user.must_change_password)

        # Clear requirement for all
        users.write({'must_change_password': False})

        # All should now have must_change_password = False
        for user in users:
            self.assertFalse(user.must_change_password)

    def test_edge_cases(self):
        """Test edge cases and error conditions"""
        user = self.Users.create(self.test_user_vals)

        # Test with empty password
        with self.assertRaises(Exception):
            user.write({'password': ''})

        # Test with None password
        with self.assertRaises(Exception):
            user.write({'password': None})

    def test_performance_with_many_users(self):
        """Test performance with many users"""
        # Create many users (simulate real-world scenario)
        users_data = []
        for i in range(100):
            users_data.append({
                'name': f'Perf Test User {i}',
                'login': f'perftest{i}@example.com',
                'password': 'testpass123',
            })

        import time
        start_time = time.time()
        users = self.Users.create(users_data)
        creation_time = time.time() - start_time

        # Should complete within reasonable time (adjust as needed)
        self.assertLess(creation_time, 10.0, "User creation took too long")

        # All should have the flag set
        users_with_flag = users.filtered('must_change_password')
        self.assertEqual(len(users_with_flag), 100)
