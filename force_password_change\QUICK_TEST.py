#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتأكد من عمل موديول Force Password Change

استخدم هذا الملف في Python shell لـ Odoo لاختبار الموديول:
python3 odoo-bin shell -d your_database_name --addons-path=/path/to/addons
exec(open('/path/to/force_password_change/QUICK_TEST.py').read())
"""

def test_force_password_change_module():
    """اختبار شامل للموديول"""
    
    print("🧪 بدء اختبار موديول Force Password Change...")
    
    # 1. التحقق من وجود الموديول
    try:
        module = env['ir.module.module'].search([('name', '=', 'force_password_change')])
        if not module:
            print("❌ الموديول غير مثبت!")
            return False
        
        if module.state != 'installed':
            print(f"❌ حالة الموديول: {module.state} (يجب أن تكون installed)")
            return False
        
        print(f"✅ الموديول مثبت - الإصدار: {module.installed_version}")
    except Exception as e:
        print(f"❌ خطأ في فحص الموديول: {e}")
        return False
    
    # 2. التحقق من وجود الحقل
    try:
        # اختبار على المستخدم الحالي
        current_user = env.user
        if not hasattr(current_user, 'must_change_password'):
            print("❌ الحقل must_change_password غير موجود!")
            return False
        
        print(f"✅ الحقل must_change_password موجود - القيمة الحالية: {current_user.must_change_password}")
    except Exception as e:
        print(f"❌ خطأ في فحص الحقل: {e}")
        return False
    
    # 3. اختبار إنشاء مستخدم جديد
    try:
        test_user = env['res.users'].create({
            'name': 'Test User for Password Change',
            'login': f'test_user_{int(time.time())}@example.com',
            'password': 'test123456'
        })
        
        if not test_user.must_change_password:
            print("❌ المستخدم الجديد لا يحتوي على must_change_password = True")
            test_user.unlink()  # حذف المستخدم التجريبي
            return False
        
        print(f"✅ المستخدم الجديد تم إنشاؤه بنجاح مع must_change_password = True")
        
        # اختبار تغيير كلمة المرور
        test_user.change_password('test123456', 'newpassword123')
        
        if test_user.must_change_password:
            print("❌ الحقل لم يتم إزالته بعد تغيير كلمة المرور")
            test_user.unlink()
            return False
        
        print("✅ تم إزالة الحقل بعد تغيير كلمة المرور بنجاح")
        
        # حذف المستخدم التجريبي
        test_user.unlink()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء المستخدم: {e}")
        return False
    
    # 4. التحقق من وجود template
    try:
        template = env['ir.ui.view'].search([
            ('key', '=', 'force_password_change.force_password_change_form')
        ])
        
        if not template:
            print("❌ Template غير موجود!")
            return False
        
        print("✅ Template موجود ومحمل بشكل صحيح")
    except Exception as e:
        print(f"❌ خطأ في فحص template: {e}")
        return False
    
    # 5. التحقق من الصلاحيات
    try:
        access_rights = env['ir.model.access'].search([
            ('model_id.model', '=', 'res.users'),
            ('name', 'ilike', 'force_password')
        ])
        
        if not access_rights:
            print("⚠️ تحذير: صلاحيات الوصول قد تكون مفقودة")
        else:
            print("✅ صلاحيات الوصول موجودة")
    except Exception as e:
        print(f"⚠️ تحذير: خطأ في فحص الصلاحيات: {e}")
    
    # 6. اختبار الطرق الإدارية
    try:
        # اختبار force_password_change method
        current_user.force_password_change()
        if not current_user.must_change_password:
            print("❌ طريقة force_password_change لا تعمل")
            return False
        
        # اختبار clear_password_change_requirement method
        current_user.clear_password_change_requirement()
        if current_user.must_change_password:
            print("❌ طريقة clear_password_change_requirement لا تعمل")
            return False
        
        print("✅ الطرق الإدارية تعمل بشكل صحيح")
    except Exception as e:
        print(f"❌ خطأ في اختبار الطرق الإدارية: {e}")
        return False
    
    print("\n🎉 جميع الاختبارات نجحت! الموديول يعمل بشكل صحيح.")
    return True

def fix_common_issues():
    """إصلاح المشاكل الشائعة"""
    
    print("🔧 بدء إصلاح المشاكل الشائعة...")
    
    # 1. التأكد من وجود الحقل في قاعدة البيانات
    try:
        env.cr.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'res_users' 
            AND column_name = 'must_change_password'
        """)
        
        result = env.cr.fetchone()
        if not result:
            print("🔧 إضافة حقل must_change_password إلى قاعدة البيانات...")
            env.cr.execute("""
                ALTER TABLE res_users 
                ADD COLUMN must_change_password boolean DEFAULT false
            """)
            env.cr.commit()
            print("✅ تم إضافة الحقل بنجاح")
        else:
            print("✅ الحقل موجود في قاعدة البيانات")
    except Exception as e:
        print(f"❌ خطأ في إضافة الحقل: {e}")
    
    # 2. تحديث القيم الفارغة
    try:
        env.cr.execute("""
            UPDATE res_users 
            SET must_change_password = false 
            WHERE must_change_password IS NULL
        """)
        env.cr.commit()
        print("✅ تم تحديث القيم الفارغة")
    except Exception as e:
        print(f"❌ خطأ في تحديث القيم: {e}")
    
    # 3. إعادة تحميل الموديول
    try:
        module = env['ir.module.module'].search([('name', '=', 'force_password_change')])
        if module and module.state == 'installed':
            module.button_immediate_upgrade()
            print("✅ تم تحديث الموديول")
    except Exception as e:
        print(f"❌ خطأ في تحديث الموديول: {e}")

def get_module_info():
    """عرض معلومات الموديول"""
    
    print("📊 معلومات موديول Force Password Change:")
    print("=" * 50)
    
    try:
        module = env['ir.module.module'].search([('name', '=', 'force_password_change')])
        if module:
            print(f"الاسم: {module.name}")
            print(f"الحالة: {module.state}")
            print(f"الإصدار المثبت: {module.installed_version}")
            print(f"الإصدار الأحدث: {module.latest_version}")
            print(f"تاريخ التثبيت: {module.write_date}")
        
        # عدد المستخدمين الذين يحتاجون لتغيير كلمة المرور
        users_count = env['res.users'].search_count([
            ('must_change_password', '=', True),
            ('active', '=', True)
        ])
        print(f"المستخدمون الذين يحتاجون لتغيير كلمة المرور: {users_count}")
        
        # إجمالي المستخدمين النشطين
        total_users = env['res.users'].search_count([('active', '=', True)])
        print(f"إجمالي المستخدمين النشطين: {total_users}")
        
    except Exception as e:
        print(f"خطأ في عرض المعلومات: {e}")

# تشغيل الاختبارات
if __name__ == "__main__":
    import time
    
    print("🚀 بدء اختبار موديول Force Password Change")
    print("=" * 60)
    
    # عرض معلومات الموديول
    get_module_info()
    print()
    
    # تشغيل الاختبارات
    success = test_force_password_change_module()
    
    if not success:
        print("\n🔧 محاولة إصلاح المشاكل...")
        fix_common_issues()
        print("\n🔄 إعادة تشغيل الاختبارات...")
        success = test_force_password_change_module()
    
    if success:
        print("\n✅ الموديول جاهز للاستخدام!")
    else:
        print("\n❌ يرجى مراجعة الأخطاء وإصلاحها يدوياً")
    
    print("=" * 60)
