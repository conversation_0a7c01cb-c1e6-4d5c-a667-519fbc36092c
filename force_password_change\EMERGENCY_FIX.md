# 🚨 إصلاح طارئ - Force Password Change Module

## المشكلة: النظام لا يعمل (Internal Server Error)

إذا كان النظام لا يعمل بعد تثبيت أو تحديث الموديول، اتبع هذه الخطوات:

## 🔥 الحل الفوري (استعادة النظام)

### الخطوة 1: إيقاف Odoo
```bash
sudo systemctl stop odoo
# أو
sudo service odoo stop
```

### الخطوة 2: تعطيل الموديول مؤقتاً
```bash
# انتقل إلى مجلد addons
cd /path/to/odoo/addons

# أعد تسمية مجلد الموديول لتعطيله
mv force_password_change force_password_change_disabled

# أو احذفه مؤقتاً
rm -rf force_password_change
```

### الخطوة 3: إعادة تشغيل Odoo
```bash
sudo systemctl start odoo
# أو
sudo service odoo start
```

**الآن يجب أن يعمل النظام بشكل طبيعي.**

## 🔧 إصلاح الموديول (بعد استعادة النظام)

### الحل الآمن - إزالة override للـ login

تم تحديث الموديول ليكون أكثر أماناً:

1. **إزالة override لـ `/web/login`** - لا يتداخل مع تسجيل الدخول الأساسي
2. **استخدام route منفصل** - `/web/force_password_change` فقط
3. **معالجة أفضل للأخطاء** - تجنب تعطيل النظام

### تثبيت النسخة الآمنة

```bash
# 1. تأكد من إيقاف Odoo
sudo systemctl stop odoo

# 2. انسخ النسخة المحدثة
cp -r force_password_change /path/to/odoo/addons/

# 3. أعد تشغيل Odoo
sudo systemctl start odoo

# 4. ثبت الموديول من واجهة Odoo
# Apps > Update Apps List > Install Force Password Change
```

## 🎯 كيفية استخدام النسخة الآمنة

### للمديرين:
1. أنشئ مستخدم جديد كالمعتاد
2. سيتم تعيين `must_change_password = True` تلقائياً
3. أرسل للمستخدم رابط: `https://your-domain.com/web/force_password_change`

### للمستخدمين:
1. سجل دخول عادي إلى النظام
2. إذا كان لديك `must_change_password = True`، اذهب إلى `/web/force_password_change`
3. غير كلمة المرور
4. سيتم توجيهك للنظام تلقائياً

## 🔄 إضافة التوجيه التلقائي (اختياري)

إذا كنت تريد التوجيه التلقائي، يمكنك إضافة JavaScript بسيط:

### إضافة إلى template الرئيسي:
```javascript
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من حالة المستخدم عند تحميل الصفحة
    fetch('/web/dataset/call_kw/res.users/read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model: 'res.users',
            method: 'read',
            args: [[odoo.session_info.uid], ['must_change_password']],
            kwargs: {}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.result && data.result[0] && data.result[0].must_change_password) {
            window.location.href = '/web/force_password_change';
        }
    })
    .catch(error => console.log('Password check error:', error));
});
</script>
```

## 🧪 اختبار النسخة الآمنة

```python
# في Python shell لـ Odoo
# اختبار إنشاء مستخدم
user = env['res.users'].create({
    'name': 'Test User',
    'login': '<EMAIL>',
    'password': 'test123456'
})

print(f"Must change password: {user.must_change_password}")

# اختبار تغيير كلمة المرور
user.change_password('test123456', 'newpassword123')
print(f"After password change: {user.must_change_password}")
```

## 📋 قائمة التحقق

بعد تطبيق الإصلاح:

- [ ] النظام يعمل بشكل طبيعي
- [ ] يمكن تسجيل الدخول بدون مشاكل
- [ ] الموديول مثبت ويعمل
- [ ] إنشاء مستخدم جديد يعمل
- [ ] صفحة `/web/force_password_change` تعمل
- [ ] تغيير كلمة المرور يعمل

## 🔍 استكشاف الأخطاء

### إذا استمرت المشاكل:

1. **تحقق من السجلات:**
```bash
tail -f /var/log/odoo/odoo.log | grep -i error
```

2. **تحقق من قاعدة البيانات:**
```sql
-- التحقق من وجود الحقل
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'res_users' AND column_name = 'must_change_password';

-- إضافة الحقل إذا لم يكن موجود
ALTER TABLE res_users ADD COLUMN IF NOT EXISTS must_change_password boolean DEFAULT false;
```

3. **إعادة تثبيت كاملة:**
```bash
# حذف الموديول
rm -rf /path/to/odoo/addons/force_password_change

# إعادة تشغيل Odoo
sudo systemctl restart odoo

# إعادة نسخ الموديول
cp -r force_password_change /path/to/odoo/addons/

# إعادة تشغيل Odoo
sudo systemctl restart odoo

# تثبيت من واجهة Odoo
```

## 📞 الدعم

إذا لم تنجح هذه الحلول:

1. تأكد من استخدام Odoo 15 Community Edition
2. تحقق من عدم وجود موديولات متضاربة
3. راجع سجلات النظام للحصول على تفاصيل الخطأ
4. جرب تثبيت الموديول على قاعدة بيانات جديدة للاختبار

## ⚠️ تحذير مهم

**لا تطبق تحديثات على بيئة الإنتاج مباشرة!**

1. اختبر دائماً على بيئة تطوير أولاً
2. خذ نسخة احتياطية من قاعدة البيانات
3. اختبر جميع الوظائف قبل النشر
4. احتفظ بخطة للعودة للنسخة السابقة
