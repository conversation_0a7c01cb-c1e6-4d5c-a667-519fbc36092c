# أمثلة عملية - Force Password Change Module

## سيناريوهات الاستخدام الشائعة

### 1. إنشاء مستخدم جديد (للمديرين)

```python
# في Python shell أو من خلال كود مخصص
new_user = env['res.users'].create({
    'name': 'أحمد محمد',
    'login': '<EMAIL>',
    'email': '<EMAIL>',
    'password': 'temp123456',  # كلمة مرور مؤقتة
    'groups_id': [(6, 0, [env.ref('base.group_user').id])]
})

# الحقل must_change_password سيتم تعيينه تلقائياً إلى True
print(f"Must change password: {new_user.must_change_password}")  # True
```

### 2. فرض تغيير كلمة المرور لمستخدم موجود

```python
# البحث عن المستخدم
user = env['res.users'].search([('login', '=', '<EMAIL>')])

# فرض تغيير كلمة المرور
user.force_password_change()

# أو مباشرة
user.write({'must_change_password': True})
```

### 3. فرض تغيير كلمة المرور لمجموعة من المستخدمين

```python
# جميع المستخدمين في قسم معين
department_users = env['res.users'].search([
    ('department_id.name', '=', 'IT Department')
])

# فرض تغيير كلمة المرور للجميع
for user in department_users:
    user.force_password_change()

# أو بطريقة أكثر كفاءة
department_users.write({'must_change_password': True})
```

### 4. إلغاء متطلب تغيير كلمة المرور

```python
# للمستخدم الواحد
user = env['res.users'].search([('login', '=', '<EMAIL>')])
user.clear_password_change_requirement()

# أو مباشرة
user.write({'must_change_password': False})
```

## أمثلة على الاستعلامات المفيدة

### 1. عرض جميع المستخدمين الذين يحتاجون لتغيير كلمة المرور

```python
users_need_change = env['res.users'].search([
    ('must_change_password', '=', True),
    ('active', '=', True)
])

for user in users_need_change:
    print(f"User: {user.name} ({user.login}) - Department: {user.department_id.name}")
```

### 2. إحصائيات المستخدمين

```python
# إجمالي المستخدمين
total_users = env['res.users'].search_count([('active', '=', True)])

# المستخدمين الذين يحتاجون لتغيير كلمة المرور
users_need_change = env['res.users'].search_count([
    ('must_change_password', '=', True),
    ('active', '=', True)
])

# النسبة المئوية
percentage = (users_need_change / total_users) * 100 if total_users > 0 else 0

print(f"Total active users: {total_users}")
print(f"Users needing password change: {users_need_change}")
print(f"Percentage: {percentage:.2f}%")
```

### 3. المستخدمين حسب القسم

```python
# تجميع المستخدمين حسب القسم
users_by_dept = env['res.users'].read_group(
    domain=[('must_change_password', '=', True), ('active', '=', True)],
    fields=['department_id'],
    groupby=['department_id']
)

for group in users_by_dept:
    dept_name = group['department_id'][1] if group['department_id'] else 'No Department'
    count = group['department_id_count']
    print(f"Department: {dept_name} - Users: {count}")
```

## أمثلة على التخصيص

### 1. إنشاء مستخدم بدون متطلب تغيير كلمة المرور

```python
# للمستخدمين الخاصين (مثل حسابات النظام)
system_user = env['res.users'].create({
    'name': 'System User',
    'login': '<EMAIL>',
    'password': 'secure_system_password',
    'must_change_password': False,  # تعيين صريح
    'groups_id': [(6, 0, [env.ref('base.group_system').id])]
})
```

### 2. تعيين تاريخ انتهاء لكلمة المرور

```python
from datetime import datetime, timedelta

# تعيين انتهاء كلمة المرور بعد 90 يوم
user = env['res.users'].search([('login', '=', '<EMAIL>')])
expiry_date = datetime.now() + timedelta(days=90)

# إذا كان لديك حقل مخصص لتاريخ الانتهاء
# user.write({'password_expiry_date': expiry_date})
```

### 3. إرسال تنبيه بالبريد الإلكتروني

```python
# إرسال تنبيه للمستخدمين الذين يحتاجون لتغيير كلمة المرور
users_need_change = env['res.users'].search([
    ('must_change_password', '=', True),
    ('active', '=', True)
])

for user in users_need_change:
    if user.email:
        # إرسال بريد إلكتروني
        mail_values = {
            'subject': 'تغيير كلمة المرور مطلوب',
            'body_html': f'''
                <p>مرحباً {user.name},</p>
                <p>يجب عليك تغيير كلمة المرور عند تسجيل الدخول التالي.</p>
                <p><a href="{env['ir.config_parameter'].get_param('web.base.url')}/web/login">تسجيل الدخول</a></p>
            ''',
            'email_to': user.email,
        }
        env['mail.mail'].create(mail_values).send()
```

## أمثلة على التقارير

### 1. تقرير يومي للمديرين

```python
@api.model
def generate_daily_password_report(self):
    """تقرير يومي عن حالة كلمات المرور"""
    
    # البيانات الأساسية
    total_users = self.search_count([('active', '=', True)])
    users_need_change = self.search_count([
        ('must_change_password', '=', True),
        ('active', '=', True)
    ])
    
    # المستخدمين الجدد اليوم
    today = fields.Date.today()
    new_users_today = self.search_count([
        ('create_date', '>=', today),
        ('active', '=', True)
    ])
    
    # تجميع حسب القسم
    dept_data = self.read_group(
        domain=[('must_change_password', '=', True), ('active', '=', True)],
        fields=['department_id'],
        groupby=['department_id']
    )
    
    report = {
        'date': today,
        'total_users': total_users,
        'users_need_change': users_need_change,
        'new_users_today': new_users_today,
        'percentage_need_change': (users_need_change / total_users * 100) if total_users > 0 else 0,
        'departments': dept_data
    }
    
    return report
```

### 2. تقرير أسبوعي مفصل

```python
@api.model
def generate_weekly_password_report(self):
    """تقرير أسبوعي مفصل"""
    
    from datetime import datetime, timedelta
    
    # تاريخ بداية ونهاية الأسبوع
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    # المستخدمين الذين غيروا كلمة المرور هذا الأسبوع
    password_changed = self.search([
        ('write_date', '>=', start_date),
        ('write_date', '<=', end_date),
        ('must_change_password', '=', False)
    ])
    
    # المستخدمين الجدد هذا الأسبوع
    new_users = self.search([
        ('create_date', '>=', start_date),
        ('create_date', '<=', end_date),
        ('active', '=', True)
    ])
    
    report = {
        'week_start': start_date.date(),
        'week_end': end_date.date(),
        'passwords_changed': len(password_changed),
        'new_users_created': len(new_users),
        'users_still_need_change': self.search_count([
            ('must_change_password', '=', True),
            ('active', '=', True)
        ])
    }
    
    return report
```

## أمثلة على المهام المجدولة

### 1. تذكير يومي للمستخدمين

```python
@api.model
def send_daily_password_reminders(self):
    """مهمة مجدولة لإرسال تذكيرات يومية"""
    
    users_need_change = self.search([
        ('must_change_password', '=', True),
        ('active', '=', True),
        ('email', '!=', False)
    ])
    
    for user in users_need_change:
        # تحقق من آخر مرة تم إرسال تذكير فيها
        last_reminder = user.last_password_reminder_date
        if not last_reminder or (fields.Date.today() - last_reminder).days >= 1:
            
            # إرسال تذكير
            self._send_password_reminder(user)
            
            # تحديث تاريخ آخر تذكير
            user.last_password_reminder_date = fields.Date.today()

def _send_password_reminder(self, user):
    """إرسال تذكير بتغيير كلمة المرور"""
    mail_values = {
        'subject': 'تذكير: تغيير كلمة المرور مطلوب',
        'body_html': f'''
            <p>مرحباً {user.name},</p>
            <p>هذا تذكير بأنه يجب عليك تغيير كلمة المرور.</p>
            <p><a href="{self.env['ir.config_parameter'].get_param('web.base.url')}/web/login">تسجيل الدخول الآن</a></p>
        ''',
        'email_to': user.email,
    }
    self.env['mail.mail'].create(mail_values).send()
```

### 2. تنظيف دوري للبيانات

```python
@api.model
def cleanup_inactive_users_password_flags(self):
    """تنظيف علامات تغيير كلمة المرور للمستخدمين غير النشطين"""
    
    inactive_users = self.search([
        ('must_change_password', '=', True),
        ('active', '=', False)
    ])
    
    if inactive_users:
        inactive_users.write({'must_change_password': False})
        _logger.info(f'Cleared password change flags for {len(inactive_users)} inactive users')
```

## أمثلة على التكامل مع أنظمة أخرى

### 1. التكامل مع Active Directory

```python
@api.model
def sync_with_active_directory(self):
    """مزامنة مع Active Directory"""
    
    # منطق الاتصال بـ AD
    ad_users = self._get_ad_users()  # دالة مخصصة للحصول على مستخدمي AD
    
    for ad_user in ad_users:
        odoo_user = self.search([('login', '=', ad_user['email'])])
        
        if odoo_user:
            # إذا كان المستخدم موجود في AD ولديه كلمة مرور حديثة
            if ad_user['password_last_set'] > odoo_user.write_date:
                odoo_user.write({'must_change_password': False})
```

### 2. التكامل مع نظام إدارة الهوية

```python
@api.model
def integrate_with_identity_management(self):
    """التكامل مع نظام إدارة الهوية"""
    
    # الحصول على المستخدمين من نظام إدارة الهوية
    idm_users = self._get_idm_users()
    
    for idm_user in idm_users:
        if idm_user['force_password_change']:
            odoo_user = self.search([('login', '=', idm_user['username'])])
            if odoo_user:
                odoo_user.write({'must_change_password': True})
```

هذه الأمثلة توضح كيفية استخدام الموديول في سيناريوهات مختلفة وتخصيصه حسب احتياجاتك.
