# -*- coding: utf-8 -*-

import logging
from odoo import http, _
from odoo.http import request
from odoo.exceptions import AccessDenied, UserError
from odoo.tools import ustr

_logger = logging.getLogger(__name__)


class ForcePasswordChangeController(http.Controller):
    """Controller to handle forced password changes - Safe version"""

    @http.route('/web/force_password_change', type='http', auth="user", methods=['GET', 'POST'], csrf=False)
    def force_password_change(self, **kw):
        """Handle forced password change page"""

        try:
            user = request.env.user

            # If user doesn't need to change password, redirect to home
            if not hasattr(user, 'must_change_password') or not user.must_change_password:
                return request.redirect('/web')

            values = {}

            if request.httprequest.method == 'POST':
                old_password = kw.get('old_password', '')
                new_password = kw.get('new_password', '')
                confirm_password = kw.get('confirm_password', '')

                try:
                    # Validate inputs
                    if not old_password:
                        values['error'] = _('Current password is required')
                    elif not new_password:
                        values['error'] = _('New password is required')
                    elif len(new_password) < 8:
                        values['error'] = _('New password must be at least 8 characters long')
                    elif new_password != confirm_password:
                        values['error'] = _('New password and confirmation do not match')
                    elif old_password == new_password:
                        values['error'] = _('New password must be different from current password')
                    else:
                        # Try to change password
                        try:
                            user.change_password(old_password, new_password)
                            _logger.info(f"Password successfully changed for user {user.login}")

                            # Redirect to home page after successful password change
                            return request.redirect('/web')

                        except AccessDenied:
                            values['error'] = _('Current password is incorrect')
                        except UserError as e:
                            values['error'] = ustr(e)
                        except Exception as e:
                            _logger.error(f"Error changing password for user {user.login}: {e}")
                            values['error'] = _('An error occurred while changing password. Please try again.')

                except Exception as e:
                    _logger.error(f"Unexpected error in password change: {e}")
                    values['error'] = _('An unexpected error occurred. Please try again.')

            # Render password change form
            values.update({
                'user_name': user.name,
                'user_login': user.login,
            })

            response = request.render('force_password_change.force_password_change_form', values)
            response.headers['X-Frame-Options'] = 'DENY'
            return response

        except Exception as e:
            _logger.error(f"Critical error in force_password_change: {e}")
            # Fallback: redirect to home page
            return request.redirect('/web')
