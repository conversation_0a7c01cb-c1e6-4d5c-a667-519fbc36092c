# -*- coding: utf-8 -*-

import json
import logging
from odoo import http, _
from odoo.http import request
from odoo.addons.web.controllers.main import Home
from odoo.exceptions import AccessDenied, UserError
from odoo.tools import ustr

_logger = logging.getLogger(__name__)


class ForcePasswordChangeController(Home):
    """Controller to handle forced password changes"""

    @http.route('/web/login', type='http', auth="none", methods=['GET', 'POST'], csrf=False)
    def web_login(self, redirect=None, **kw):
        """Override web login to check for password change requirement"""

        # Handle POST request (login attempt)
        if request.httprequest.method == 'POST':
            old_uid = request.uid
            try:
                # Call parent login method
                response = super(ForcePasswordChangeController, self).web_login(redirect=redirect, **kw)

                # Check if login was successful
                if request.uid and request.uid != old_uid:
                    try:
                        # Login successful, check if user needs to change password
                        user = request.env['res.users'].sudo().browse(request.uid)
                        if user.exists() and hasattr(user, 'must_change_password') and user.must_change_password:
                            _logger.info(f"User {user.login} must change password, redirecting to change password page")
                            # Redirect to password change page
                            return request.redirect('/web/force_password_change')
                    except Exception as e:
                        _logger.error(f"Error checking password change requirement: {e}")
                        # Continue with normal login if there's an error
                        pass

                return response
                
            except AccessDenied as e:
                # Login failed, return normal error
                request.uid = old_uid
                values = request.params.copy()
                try:
                    values['databases'] = http.db_list()
                except Exception:
                    values['databases'] = None
                
                if request.httprequest.method == 'POST':
                    values['error'] = _("Wrong login/password")
                
                response = request.render('web.login', values)
                response.headers['X-Frame-Options'] = 'DENY'
                return response
        
        # Handle GET request (show login page)
        return super(ForcePasswordChangeController, self).web_login(redirect=redirect, **kw)

    @http.route('/web/force_password_change', type='http', auth="user", methods=['GET', 'POST'], csrf=False)
    def force_password_change(self, **kw):
        """Handle forced password change page"""

        try:
            user = request.env.user

            # If user doesn't need to change password, redirect to home
            if not hasattr(user, 'must_change_password') or not user.must_change_password:
                return request.redirect('/web')

            values = {}
        
        if request.httprequest.method == 'POST':
            old_password = kw.get('old_password', '')
            new_password = kw.get('new_password', '')
            confirm_password = kw.get('confirm_password', '')
            
            try:
                # Validate inputs
                if not old_password:
                    values['error'] = _('Current password is required')
                elif not new_password:
                    values['error'] = _('New password is required')
                elif len(new_password) < 8:
                    values['error'] = _('New password must be at least 8 characters long')
                elif new_password != confirm_password:
                    values['error'] = _('New password and confirmation do not match')
                elif old_password == new_password:
                    values['error'] = _('New password must be different from current password')
                else:
                    # Try to change password
                    try:
                        user.change_password(old_password, new_password)
                        _logger.info(f"Password successfully changed for user {user.login}")
                        
                        # Redirect to home page after successful password change
                        return request.redirect('/web')
                        
                    except AccessDenied:
                        values['error'] = _('Current password is incorrect')
                    except UserError as e:
                        values['error'] = ustr(e)
                    except Exception as e:
                        _logger.error(f"Error changing password for user {user.login}: {e}")
                        values['error'] = _('An error occurred while changing password. Please try again.')
                        
            except Exception as e:
                _logger.error(f"Unexpected error in password change: {e}")
                values['error'] = _('An unexpected error occurred. Please try again.')
        
            # Render password change form
            values.update({
                'user_name': user.name,
                'user_login': user.login,
            })

            response = request.render('force_password_change.force_password_change_form', values)
            response.headers['X-Frame-Options'] = 'DENY'
            return response

        except Exception as e:
            _logger.error(f"Critical error in force_password_change: {e}")
            # Fallback: redirect to home page
            return request.redirect('/web')
