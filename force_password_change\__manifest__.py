{
    'name': 'Force Password Change',
    'version': '********.0',
    'category': 'Authentication',
    'summary': 'Force new users to change their password on first login',
    'description': """
Force Password Change
=====================

This module forces new users to change their password on their first login.

Features:
---------
* Automatically sets a flag for new users created by administrators
* Redirects users to password change screen on first login
* Prevents access to the system until password is changed
* Fully compatible with Odoo 15 Community Edition

Installation:
-------------
1. Copy this module to your addons directory
2. Update the app list
3. Install the module

Usage:
------
When an administrator creates a new user, the system automatically sets
the 'must_change_password' flag. When this user logs in for the first time,
they will be redirected to a password change screen and cannot access the
system until they change their password.

Technical Details:
------------------
* Extends res.users model with must_change_password field
* Overrides web login controller to check password change requirement
* Provides secure password change interface
* Automatically updates flag after successful password change
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'web',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/data.xml',
        'views/force_password_change_views.xml',
        'views/templates.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'force_password_change/static/src/js/force_password_redirect.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'sequence': 100,
}
