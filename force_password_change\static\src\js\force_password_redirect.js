odoo.define('force_password_change.redirect', function (require) {
    'use strict';

    var core = require('web.core');
    var session = require('web.session');
    var rpc = require('web.rpc');

    /**
     * Check if user needs to change password and redirect if necessary
     */
    function checkPasswordChangeRequirement() {
        // Only check if user is logged in
        if (!session.uid || session.uid === 1) {
            return;
        }

        // Avoid infinite redirects
        if (window.location.pathname === '/web/force_password_change') {
            return;
        }

        // Check user's password change requirement
        rpc.query({
            model: 'res.users',
            method: 'read',
            args: [[session.uid], ['must_change_password']],
        }).then(function (result) {
            if (result && result.length > 0 && result[0].must_change_password) {
                console.log('User must change password, redirecting...');
                window.location.href = '/web/force_password_change';
            }
        }).catch(function (error) {
            console.log('Error checking password requirement:', error);
            // Don't redirect on error to avoid breaking the system
        });
    }

    // Check when the page loads
    $(document).ready(function() {
        // Wait a bit for the session to be fully loaded
        setTimeout(checkPasswordChangeRequirement, 1000);
    });

    // Also check when navigating (for SPA behavior)
    $(document).on('click', 'a[href^="/web"]', function() {
        setTimeout(checkPasswordChangeRequirement, 500);
    });

    return {
        checkPasswordChangeRequirement: checkPasswordChangeRequirement
    };
});
