# دليل استكشاف الأخطاء - Force Password Change Module

## المشاكل الشائعة والحلول

### 1. خطأ Internal Server Error عند تسجيل الخروج

**الأعراض:**
- ر<PERSON>الة "Internal Server Error" عند الضغط على Logout
- عدم القدرة على تسجيل الخروج بشكل طبيعي

**السبب:**
- تضارب في route `/web/session/logout`

**الحل:**
- تأكد من أنك تستخدم الإصدار 15.0.1.0.1 أو أحدث
- إذا كنت تستخدم إصدار أقدم، قم بتحديث الموديول

**خطوات التحديث:**
```bash
# 1. أوقف خدمة Odoo
sudo systemctl stop odoo

# 2. استبدل ملفات الموديول بالإصدار الجديد
cp -r force_password_change /path/to/odoo/addons/

# 3. أعد تشغيل Odoo
sudo systemctl start odoo

# 4. حدث الموديول من واجهة Odoo
```

### 2. خطأ في xpath عند تثبيت الموديول

**الأعراض:**
```
Element '<xpath expr="//filter[@name='inactive']">' cannot be located in parent view
```

**السبب:**
- اختلاف في أسماء الفلاتر بين إصدارات Odoo

**الحل:**
- تأكد من استخدام الإصدار المحدث من الموديول
- الإصدار الحالي يستخدم `filter[@name='Inactive']` بدلاً من `inactive`

### 3. المستخدمون لا يتم توجيههم لشاشة تغيير كلمة المرور

**الأعراض:**
- المستخدم الجديد يدخل النظام مباشرة دون تغيير كلمة المرور

**التشخيص:**
```python
# تحقق من حالة المستخدم في Python shell
user = env['res.users'].search([('login', '=', 'username')])
print(f"Must change password: {user.must_change_password}")
```

**الحلول المحتملة:**

#### أ. تحقق من تفعيل الحقل
```python
# في Python shell
user = env['res.users'].search([('login', '=', 'username')])
user.write({'must_change_password': True})
```

#### ب. تحقق من سجلات النظام
```bash
# تحقق من سجلات Odoo
tail -f /var/log/odoo/odoo.log | grep force_password_change
```

#### ج. تحقق من controller
- تأكد من أن controller يعمل بشكل صحيح
- تحقق من عدم وجود تضارب مع موديولات أخرى

### 4. شاشة تغيير كلمة المرور لا تظهر بشكل صحيح

**الأعراض:**
- صفحة فارغة أو خطأ في التحميل
- عناصر CSS مفقودة

**الحلول:**

#### أ. تحقق من template
```bash
# تحقق من وجود ملف template
ls -la /path/to/addons/force_password_change/views/templates.xml
```

#### ب. تحقق من assets
- تأكد من تحميل CSS و JavaScript بشكل صحيح
- امسح cache المتصفح

#### ج. تحقق من الصلاحيات
```bash
# تأكد من صلاحيات الملفات
chmod -R 644 /path/to/addons/force_password_change/
chmod 755 /path/to/addons/force_password_change/
```

### 5. خطأ في تغيير كلمة المرور

**الأعراض:**
- رسالة خطأ عند محاولة تغيير كلمة المرور
- عدم قبول كلمة المرور الجديدة

**التشخيص:**
```python
# تحقق من إعدادات كلمة المرور
config = env['ir.config_parameter']
min_length = config.get_param('force_password_change.min_password_length', '8')
print(f"Minimum password length: {min_length}")
```

**الحلول:**

#### أ. تحقق من طول كلمة المرور
- كلمة المرور يجب أن تكون 8 أحرف على الأقل

#### ب. تحقق من كلمة المرور الحالية
- تأكد من إدخال كلمة المرور الحالية بشكل صحيح

#### ج. تحقق من تطابق كلمة المرور
- تأكد من تطابق كلمة المرور الجديدة مع التأكيد

### 6. مشاكل الصلاحيات

**الأعراض:**
- خطأ "Access Denied"
- عدم ظهور القوائم الإدارية

**الحل:**
```python
# في Python shell، تحقق من الصلاحيات
user = env.user
print(f"User groups: {user.groups_id.mapped('name')}")

# إضافة صلاحيات إدارية إذا لزم الأمر
admin_group = env.ref('base.group_system')
user.write({'groups_id': [(4, admin_group.id)]})
```

### 7. مشاكل التوافق مع موديولات أخرى

**الأعراض:**
- تضارب مع موديولات authentication أخرى
- سلوك غير متوقع في تسجيل الدخول

**الحل:**
1. **تحديد الموديولات المتضاربة:**
```python
# ابحث عن موديولات تعدل res.users
modules = env['ir.module.module'].search([
    ('state', '=', 'installed'),
    ('name', 'ilike', 'auth')
])
for module in modules:
    print(f"Module: {module.name}")
```

2. **تحقق من ترتيب التحميل:**
- تأكد من أن الموديول يتم تحميله بالترتيب الصحيح
- أضف dependencies إذا لزم الأمر

### 8. مشاكل الأداء

**الأعراض:**
- بطء في تسجيل الدخول
- استهلاك عالي للذاكرة

**الحلول:**

#### أ. تحسين الاستعلامات
```python
# تحقق من عدد المستخدمين الذين يحتاجون لتغيير كلمة المرور
users_count = env['res.users'].search_count([('must_change_password', '=', True)])
print(f"Users requiring password change: {users_count}")
```

#### ب. تنظيف البيانات القديمة
```python
# إزالة العلامة من المستخدمين القدامى إذا لزم الأمر
old_users = env['res.users'].search([
    ('must_change_password', '=', True),
    ('login_date', '<', '2024-01-01')
])
old_users.write({'must_change_password': False})
```

## أدوات التشخيص

### 1. فحص حالة الموديول
```python
# في Python shell
module = env['ir.module.module'].search([('name', '=', 'force_password_change')])
print(f"Module state: {module.state}")
print(f"Module version: {module.installed_version}")
```

### 2. فحص المستخدمين
```python
# إحصائيات المستخدمين
total_users = env['res.users'].search_count([])
users_must_change = env['res.users'].search_count([('must_change_password', '=', True)])
print(f"Total users: {total_users}")
print(f"Users must change password: {users_must_change}")
```

### 3. فحص السجلات
```bash
# فحص سجلات Odoo للأخطاء
grep -i "force_password_change" /var/log/odoo/odoo.log | tail -20

# فحص أخطاء Python
grep -i "error\|exception" /var/log/odoo/odoo.log | grep force_password_change
```

## الحصول على المساعدة

إذا لم تحل هذه الحلول مشكلتك:

1. **جمع المعلومات:**
   - إصدار Odoo
   - إصدار الموديول
   - رسائل الخطأ الكاملة
   - خطوات إعادة إنتاج المشكلة

2. **فحص السجلات:**
   - سجلات Odoo
   - سجلات خادم الويب
   - سجلات قاعدة البيانات

3. **التواصل للدعم:**
   - قدم جميع المعلومات المجمعة
   - اذكر الحلول التي جربتها
   - أرفق ملفات السجلات ذات الصلة
