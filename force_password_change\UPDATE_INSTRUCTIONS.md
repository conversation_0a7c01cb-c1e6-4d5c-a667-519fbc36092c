# تعليمات التحديث - Force Password Change Module

## مشكلة Internal Server Error عند تسجيل الدخول

إذا كنت تواجه مشكلة "Internal Server Error" عند تسجيل الدخول مع مستخدم لديه `must_change_password = True`، اتبع هذه الخطوات:

## الحل السريع

### 1. تحديث الموديول

```bash
# أوقف خدمة Odoo
sudo systemctl stop odoo

# استبدل ملفات الموديول بالنسخة المحدثة
cp -r force_password_change /path/to/odoo/addons/

# أعد تشغيل Odoo
sudo systemctl start odoo
```

### 2. تحديث الموديول من واجهة Odoo

1. سجل دخول كمدير
2. اذهب إلى **Apps** (التطبيقات)
3. ابحث عن "Force Password Change"
4. اضغط على **Upgrade** (تحديث)

### 3. التحقق من قاعدة البيانات

إذا استمرت المشكلة، تحقق من وجود الحقل في قاعدة البيانات:

```sql
-- الاتصال بقاعدة البيانات
psql -U odoo -d your_database_name

-- التحقق من وجود العمود
\d res_users

-- إذا لم يكن العمود موجود، أضفه يدوياً
ALTER TABLE res_users ADD COLUMN must_change_password boolean DEFAULT false;

-- تحديث القيم الموجودة
UPDATE res_users SET must_change_password = false WHERE must_change_password IS NULL;
```

## الحل المتقدم

### 1. فحص السجلات

```bash
# فحص سجلات Odoo للحصول على تفاصيل الخطأ
tail -f /var/log/odoo/odoo.log | grep -i error

# البحث عن أخطاء محددة
grep -i "force_password_change" /var/log/odoo/odoo.log | tail -20
```

### 2. اختبار الحقل يدوياً

```python
# في Python shell لـ Odoo
user = env['res.users'].search([('login', '=', 'your_test_user')])
print(f"User exists: {user.exists()}")
print(f"Has field: {hasattr(user, 'must_change_password')}")
print(f"Field value: {getattr(user, 'must_change_password', 'NOT_FOUND')}")

# تعيين القيمة يدوياً
user.write({'must_change_password': True})
```

### 3. إعادة إنشاء الحقل

إذا كان الحقل مفقود، يمكنك إعادة إنشاؤه:

```python
# في Python shell
# إضافة الحقل إذا لم يكن موجود
env.cr.execute("""
    ALTER TABLE res_users 
    ADD COLUMN IF NOT EXISTS must_change_password boolean DEFAULT false
""")

# تحديث جميع المستخدمين
env.cr.execute("""
    UPDATE res_users 
    SET must_change_password = false 
    WHERE must_change_password IS NULL
""")

env.cr.commit()
```

## اختبار الإصلاح

### 1. اختبار أساسي

```python
# إنشاء مستخدم اختبار
test_user = env['res.users'].create({
    'name': 'Test User',
    'login': '<EMAIL>',
    'password': 'test123456'
})

print(f"Must change password: {test_user.must_change_password}")  # يجب أن يكون True
```

### 2. اختبار تسجيل الدخول

1. أنشئ مستخدم جديد من واجهة Odoo
2. تأكد من أن `must_change_password` مُفعل
3. سجل خروج وحاول تسجيل الدخول بالمستخدم الجديد
4. يجب أن يتم توجيهك لشاشة تغيير كلمة المرور

## استكشاف أخطاء إضافية

### مشكلة Template غير موجود

إذا كان الخطأ متعلق بـ template:

```python
# التحقق من وجود template
template = env['ir.ui.view'].search([
    ('key', '=', 'force_password_change.force_password_change_form')
])
print(f"Template exists: {template.exists()}")

# إذا لم يكن موجود، أعد تحميل البيانات
env['ir.module.module'].search([
    ('name', '=', 'force_password_change')
]).button_immediate_upgrade()
```

### مشكلة Controller غير مسجل

```python
# التحقق من تسجيل controller
routes = env['ir.http']._get_converters()
print("Available routes:", [r for r in routes if 'force_password' in str(r)])
```

## الحل النهائي (إذا فشل كل شيء)

### إعادة تثبيت كاملة

```bash
# 1. إلغاء تثبيت الموديول
# من واجهة Odoo: Apps > Force Password Change > Uninstall

# 2. حذف الملفات القديمة
rm -rf /path/to/odoo/addons/force_password_change

# 3. نسخ الملفات الجديدة
cp -r force_password_change /path/to/odoo/addons/

# 4. إعادة تشغيل Odoo
sudo systemctl restart odoo

# 5. إعادة تثبيت الموديول
# من واجهة Odoo: Apps > Update Apps List > Install Force Password Change
```

## التحقق من نجاح الإصلاح

بعد تطبيق الإصلاحات:

1. ✅ تسجيل الدخول العادي يعمل بدون أخطاء
2. ✅ المستخدمون مع `must_change_password = False` يدخلون مباشرة
3. ✅ المستخدمون مع `must_change_password = True` يتم توجيههم لشاشة تغيير كلمة المرور
4. ✅ شاشة تغيير كلمة المرور تظهر بشكل صحيح
5. ✅ تغيير كلمة المرور يعمل ويزيل العلامة

## الدعم

إذا استمرت المشكلة بعد تطبيق جميع الحلول:

1. تأكد من أنك تستخدم Odoo 15 Community Edition
2. تحقق من عدم وجود موديولات متضاربة
3. راجع سجلات النظام للحصول على تفاصيل أكثر
4. تواصل مع الدعم مع تقديم:
   - إصدار Odoo
   - رسائل الخطأ الكاملة
   - خطوات إعادة إنتاج المشكلة
