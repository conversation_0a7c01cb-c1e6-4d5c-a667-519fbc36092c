# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ResUsers(models.Model):
    _inherit = 'res.users'

    must_change_password = fields.Boolean(
        string='Must Change Password',
        default=False,
        help='If checked, user will be forced to change password on next login'
    )

    @api.model
    def create(self, vals):
        """Override create method to automatically set must_change_password for new users"""
        try:
            # Set must_change_password to True for new users created by admin
            # unless explicitly set to False
            if 'must_change_password' not in vals:
                vals['must_change_password'] = True

            user = super(ResUsers, self).create(vals)
            _logger.info(f"New user created: {user.login}, must_change_password: {user.must_change_password}")
            return user
        except Exception as e:
            _logger.error(f"Error creating user: {e}")
            # Remove the custom field if it causes issues
            if 'must_change_password' in vals:
                del vals['must_change_password']
            return super(ResUsers, self).create(vals)

    def write(self, vals):
        """Override write method to handle password changes"""
        # If password is being changed and user had must_change_password=True,
        # set it to False
        if 'password' in vals:
            for user in self:
                if user.must_change_password:
                    vals['must_change_password'] = False
                    _logger.info(f"Password changed for user {user.login}, clearing must_change_password flag")
        
        return super(ResUsers, self).write(vals)

    def change_password(self, old_passwd, new_passwd):
        """Override change_password method to clear the flag"""
        result = super(ResUsers, self).change_password(old_passwd, new_passwd)
        
        # Clear the must_change_password flag after successful password change
        if self.must_change_password:
            self.sudo().write({'must_change_password': False})
            _logger.info(f"Password changed via change_password method for user {self.login}")
        
        return result

    def force_password_change(self):
        """Method to manually force password change for a user"""
        self.ensure_one()
        self.sudo().write({'must_change_password': True})
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Password Change Required'),
                'message': _('User will be required to change password on next login.'),
                'type': 'success',
            }
        }

    def clear_password_change_requirement(self):
        """Method to manually clear password change requirement"""
        self.ensure_one()
        self.sudo().write({'must_change_password': False})
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Password Change Cleared'),
                'message': _('User is no longer required to change password.'),
                'type': 'success',
            }
        }
